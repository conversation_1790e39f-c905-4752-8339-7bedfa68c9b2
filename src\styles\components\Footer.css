/* Footer with gradients */
.footer {
  background: #1A1A1A;
  position: relative;
  padding: 60px 0 20px;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow: hidden;
}

/* Footer Background Gradients */
.footer-blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.footer-blur-gradient-1 {
  width: 156px;
  height: 156px;
  left: 0px;
  top: 0px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(65.272px);
}

.footer-blur-gradient-2 {
  width: 239px;
  height: 239px;
  right: -14px;
  top: 237px;
  background: #D19049;
  opacity: 0.2;
  filter: blur(100px);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 10;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 32px;
  margin-bottom: 40px;
}

/* Footer Logo */
.footer-logo {
  display: flex;
  justify-content: center;
}

.footer-logo-img {
  height: 40px;
  width: auto;
}

/* Footer Navigation */
.footer-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.footer-link {
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 1.6;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #BF4129;
}

/* Social Media */
.footer-social {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.social-link {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-icon {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1); /* Make icons white */
  transition: filter 0.3s ease;
}

.social-link:hover .social-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%); /* #BF4129 color */
}

/* Footer Bottom */
.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #404040;
}

.copyright {
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }

  .container {
    padding: 0 16px;
  }

  .footer-content {
    gap: 24px;
  }

  .footer-nav {
    flex-direction: column;
    gap: 16px;
  }

  .footer-link {
    font-size: 14px;
  }

  .footer-logo-img {
    height: 32px;
  }

  .social-icon {
    width: 20px;
    height: 20px;
  }

  .social-link {
    width: 36px;
    height: 36px;
  }

  .footer-blur-gradient-1 {
    width: 120px;
    height: 120px;
    left: -20px;
    top: 20px;
  }

  .footer-blur-gradient-2 {
    width: 180px;
    height: 180px;
    right: -40px;
    top: 180px;
  }
}
