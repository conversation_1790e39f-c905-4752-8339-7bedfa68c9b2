/* Dashboard Navigation Loader */
.dashboard-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7); /* More transparent */
  backdrop-filter: blur(30px); /* Stronger blur to hide header/footer */
  -webkit-backdrop-filter: blur(30px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100000; /* Higher z-index to cover header/footer */
  animation: fadeIn 0.3s ease-in-out;
}

.dashboard-loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  background: rgba(38, 38, 38, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

/* Animated Spinner */
.loader-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #BF4129;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #F59E0B;
  animation-delay: 0.3s;
  width: 70%;
  height: 70%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #10B981;
  animation-delay: 0.6s;
  width: 40%;
  height: 40%;
}

/* Loader Content */
.loader-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loader-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.loader-message {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-loader-overlay {
    /* Ensure full screen coverage on mobile */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
  }

  .dashboard-loader-container {
    padding: 32px 24px;
    gap: 20px;
    border-radius: 16px;
    width: 85%; /* Slightly smaller on mobile */
    max-width: 350px;
  }

  .loader-spinner {
    width: 60px;
    height: 60px;
  }

  .loader-title {
    font-size: 20px;
  }

  .loader-message {
    font-size: 14px;
  }
}
