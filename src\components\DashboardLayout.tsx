import React from 'react';
import { useNavigate } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import '../styles/components/DashboardLayout.css';

interface DashboardLayoutProps {
  children: React.ReactNode;
  showBlurGradients?: boolean;
  className?: string;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ 
  children, 
  showBlurGradients = true,
  className = ''
}) => {
  const navigate = useNavigate();

  return (
    <div className={`dashboard-layout-container ${className}`}>
      {/* Background Blur Gradients */}
      {showBlurGradients && (
        <>
          <div className="blur-gradient blur-gradient-1"></div>
          <div className="blur-gradient blur-gradient-2"></div>
          <div className="blur-gradient blur-gradient-3"></div>
          <div className="blur-gradient blur-gradient-4"></div>
          <div className="blur-gradient blur-gradient-5"></div>
        </>
      )}

      {/* Header */}
      <Header
        onNavigateToSignUp={() => navigate('/signup')}
        onNavigateToLogin={() => navigate('/login')}
        onNavigateToLanding={() => navigate('/')}
      />

      {/* Main Content */}
      <main className="dashboard-main">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default DashboardLayout;