import React from 'react';
import '../styles/components/Hero.css';
import icon1 from '../assets/icon 1.png';
import icon2 from '../assets/icon2.svg';
import icon3 from '../assets/icon3.svg';

const Hero: React.FC = () => {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          {/* Floating Icons */}
          <div className="floating-icons">
            {/* Left side icons */}
            <div className="icon-left-1">
              <img src={icon1} alt="Icon 1" className="hero-icon" />
            </div>
            <div className="icon-left-2">
              <img src={icon2} alt="Icon 2" className="hero-icon" />
            </div>

            {/* Right side icons */}
            <div className="icon-right-1">
              <img src={icon1} alt="Icon 1" className="hero-icon" />
            </div>
            <div className="icon-right-3">
              <img src={icon3} alt="Icon 3" className="hero-icon" />
            </div>
          </div>

          {/* Earn Badge */}
          <div className="earn-badge">
            <span>Earn 20% annual returns</span>
          </div>

          {/* Main Content */}
          <div className="hero-text">
            <h1 className="hero-title">
              <span className="title-grow">Grow</span>{' '}
              <span className="title-wealth">Your Wealth.</span>{' '}
              <span className="title-fund">Fund</span>{' '}
              <span className="title-future">the Future.</span>
            </h1>
            <h2 className="hero-subtitle">
              <span className="subtitle-stake">Stake with</span>{' '}
              <span className="subtitle-confidence">Confidence.</span>
            </h2>
            
            <p className="hero-description">
              Stake your ESVC tokens securely, earn daily ROI, and unlock exclusive 
              opportunities to pitch your startup ideas for funding. Transparent treasury 
              tracking, secure crypto deposits, and tiered rewards.
            </p>

            <button className="btn-primary hero-cta">
              Start Staking Now
              <span className="cta-icon">🚀</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
