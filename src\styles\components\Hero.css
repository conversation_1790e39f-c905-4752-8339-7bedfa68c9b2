.hero {
  background: var(--bg-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 120px 0 80px 0; /* Extra top padding for absolute header */
}

.hero-content {
  position: relative;
  text-align: center;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 32px;
  position: absolute;
  width: 919px;
  height: 373px;
  left: calc(50% - 919px/2 + 0.5px);
  top: 176px;
}

.hero-headline-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 16px;
  isolation: isolate;
  width: 919px;
  height: 285px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.hero-badge {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 10px;
  width: 205px;
  height: 37px;
  border-radius: 999px;
  flex: none;
  order: 0;
  flex-grow: 0;
  z-index: 0;
  background: transparent;
}

.hero-badge-text {
  width: 173px;
  height: 17px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  background: linear-gradient(267.18deg, #D19049 -33.36%, #BF4129 126.44%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.hero-title {
  width: 919px;
  height: 144px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 48px;
  line-height: 150%;
  text-align: center;
  color: #CC6754;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  z-index: 1;
}

.hero-desc {
  width: 627px;
  height: 72px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  text-align: center;
  color: #D4D4D4;
  flex: none;
  order: 2;
  flex-grow: 0;
  z-index: 2;
}

.hero-vector {
  position: absolute;
  width: 300px;
  height: 13px;
  left: 451px;
  top: 184px;
  background: #C6741B;
  flex: none;
  order: 3;
  flex-grow: 0;
  z-index: 3;
}

.hero-element04 {
  position: absolute;
  width: 176px;
  height: 71.65px;
  left: 146px;
  top: 72px;
  flex: none;
  order: 4;
  flex-grow: 0;
  z-index: 4;
}

.hero-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 238px;
  height: 56px;
  background: #BF4129;
  border-radius: 999px;
  flex: none;
  order: 1;
  flex-grow: 0;
  border: none;
  cursor: pointer;
}

.hero-btn-label {
  width: 170px;
  height: 22px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #FAFAFA;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.hero-btn-icon {
  width: 24px;
  height: 24px;
  flex: none;
  order: 2;
  flex-grow: 0;
}

/* Floating Icons */
.floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
}

/* Left side icons */
.icon-left-1 {
  position: absolute;
  width: 120px;
  height: 120px;
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 0s;
}

.icon-left-2 {
  position: absolute;
  width: 80px;
  height: 80px;
  top: 60%;
  left: 15%;
  animation: floatReverse 8s ease-in-out infinite;
  animation-delay: 2s;
}

/* Right side icons */
.icon-right-1 {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 40%;
  right: 15%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 4s;
}

.icon-right-3 {
  position: absolute;
  width: 120px;
  height: 120px;
  top: 15%;
  right: 8%;
  animation: floatSlow 10s ease-in-out infinite;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
}

/* Animation keyframes remain the same */

@keyframes floatReverse {
  0%, 100% {
    transform: translateY(0px) rotate(360deg);
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.1);
  }
}

/* Earn Badge */
.earn-badge {
  display: inline-block;
  background: rgba(232, 90, 79, 0.1);
  border: 1px solid var(--accent-orange);
  border-radius: 20px;
  padding: 8px 16px;
  color: var(--accent-orange);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 32px;
}

/* Hero Text */
.hero-text {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 64px;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 16px;
}

.title-grow {
  color: var(--accent-orange);
}

.title-wealth {
  color: var(--text-primary);
}

.title-fund {
  color: var(--accent-gold);
}

.title-future {
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 32px;
}

.subtitle-stake {
  color: var(--text-primary);
}

.subtitle-confidence {
  color: var(--accent-orange);
  border: 3px solid var(--accent-orange);
  border-radius: 12px;
  padding: 8px 16px;
  display: inline-block;
}

.hero-description {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 48px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  font-size: 18px;
  padding: 16px 32px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-icon {
  font-size: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .hero {
    padding: 60px 0;
    min-height: 80vh;
  }

  .hero-title {
    font-size: 40px;
  }

  .hero-subtitle {
    font-size: 28px;
  }

  .subtitle-confidence {
    padding: 6px 12px;
    font-size: 24px;
  }

  .hero-description {
    font-size: 16px;
    margin-bottom: 32px;
  }

  .icon-left-1 {
    width: 80px;
    height: 80px;
  }

  .icon-left-2 {
    width: 60px;
    height: 60px;
  }

  .icon-right-1 {
    width: 40px;
    height: 40px;
  }

  .icon-right-3 {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 24px;
  }

  .subtitle-confidence {
    font-size: 20px;
    padding: 4px 8px;
  }

  .floating-icons {
    opacity: 0.7;
  }
}

/* New styles from Figma */
.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 32px;
  position: absolute;
  width: 919px;
  height: 373px;
  left: calc(50% - 919px/2 + 0.5px);
  top: 176px;
}

.hero-headline-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 16px;
  isolation: isolate;
  width: 919px;
  height: 285px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.hero-badge {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 10px;
  width: 205px;
  height: 37px;
  border-radius: 999px;
  flex: none;
  order: 0;
  flex-grow: 0;
  z-index: 0;
  background: transparent;
}

.hero-badge-text {
  width: 173px;
  height: 17px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  background: linear-gradient(267.18deg, #D19049 -33.36%, #BF4129 126.44%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.hero-title {
  width: 919px;
  height: 144px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 48px;
  line-height: 150%;
  text-align: center;
  color: #CC6754;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  z-index: 1;
}

.hero-desc {
  width: 627px;
  height: 72px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  text-align: center;
  color: #D4D4D4;
  flex: none;
  order: 2;
  flex-grow: 0;
  z-index: 2;
}

.hero-vector {
  position: absolute;
  width: 300px;
  height: 13px;
  left: 451px;
  top: 184px;
  background: #C6741B;
  flex: none;
  order: 3;
  flex-grow: 0;
  z-index: 3;
}

.hero-element04 {
  position: absolute;
  width: 176px;
  height: 71.65px;
  left: 146px;
  top: 72px;
  flex: none;
  order: 4;
  flex-grow: 0;
  z-index: 4;
}

.hero-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 238px;
  height: 56px;
  background: #BF4129;
  border-radius: 999px;
  flex: none;
  order: 1;
  flex-grow: 0;
  border: none;
  cursor: pointer;
}

.hero-btn-label {
  width: 170px;
  height: 22px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #FAFAFA;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.hero-btn-icon {
  width: 24px;
  height: 24px;
  flex: none;
  order: 2;
  flex-grow: 0;
}

/* Responsive adjustments */
@media (max-width: 1000px) {
  .hero-content,
  .hero-headline-group {
    width: 95vw;
    left: 2.5vw;
    min-width: unset;
    max-width: 100vw;
  }
  .hero-title {
    width: 100%;
    font-size: 32px;
    height: auto;
  }
  .hero-desc {
    width: 90vw;
    min-width: unset;
    max-width: 100vw;
  }
}

@media (max-width: 600px) {
  .hero-content {
    width: 100vw;
    left: 0;
    padding: 0 8px;
    top: 100px;
    height: auto;
  }
  .hero-title {
    font-size: 22px;
    line-height: 1.3;
    width: 100%;
  }
  .hero-desc {
    font-size: 14px;
    width: 100%;
  }
  .hero-btn {
    width: 90vw;
    min-width: 160px;
    height: 48px;
    font-size: 15px;
  }
}
