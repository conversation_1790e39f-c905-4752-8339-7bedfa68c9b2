.comparison-table {
  background: var(--bg-primary);
  padding: 80px 0;
}

.comparison-header {
  text-align: center;
  margin-bottom: 60px;
}

.comparison-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.comparison-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
}

.comparison-content {
  max-width: 800px;
  margin: 0 auto 60px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Headers */
.comparison-header-left,
.comparison-header-right {
  padding: 24px;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
}

.comparison-header-left {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.comparison-header-right {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
}

/* Feature Items */
.feature-item {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.feature-item:last-of-type {
  border-bottom: none;
}

.feature-item.traditional {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.feature-item.esvc {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

/* Action Buttons */
.comparison-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.comparison-actions .btn-primary,
.comparison-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

/* Responsive */
@media (max-width: 768px) {
  .comparison {
    padding: 60px 0 !important;
  }

  .comparison-header {
    margin-bottom: 40px !important;
    padding: 0 20px !important;
  }

  .comparison-title {
    font-size: 32px !important;
    margin-bottom: 16px !important;
    line-height: 1.2 !important;
  }

  .comparison-subtitle {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .comparison-content {
    padding: 0 20px !important;
  }

  .comparison-grid {
    grid-template-columns: 1fr !important;
    gap: 0 !important;
    border-radius: 12px !important;
    overflow: hidden !important;
  }

  .comparison-header-left {
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 20px !important;
    text-align: center !important;
  }

  .comparison-header-right {
    padding: 20px !important;
    text-align: center !important;
  }

  .feature-item.traditional {
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 16px 20px !important;
  }

  .feature-item.esvc {
    padding: 16px 20px !important;
  }

  .feature-icon {
    font-size: 18px !important;
  }

  .feature-text {
    font-size: 14px !important;
  }

  .comparison-actions {
    flex-direction: column !important;
    align-items: center !important;
    padding: 0 20px !important;
    gap: 16px !important;
    margin-top: 40px !important;
  }

  .comparison-actions .btn-primary,
  .comparison-actions .btn-secondary {
    width: 100% !important;
    max-width: 320px !important;
    justify-content: center !important;
    padding: 16px 24px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }
}
