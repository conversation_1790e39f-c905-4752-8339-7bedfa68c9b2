import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/TradeChallenge.css';
import DashboardLayout from './DashboardLayout';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';

// Import icons
import arrowRightIcon from '../assets/arrow-right.png';
import arrowDownIcon from '../assets/arrow-down.png';
import groupImage from '../assets/Group.png';
// import informationCircleIcon from '../assets/information-circle.png';
import dollarSquareIcon from '../assets/dollar-square.png';
import walletIcon from '../assets/wallet.png';
import bitcoinConvertIcon from '../assets/bitcoin-convert.png';
import trendUp2Icon from '../assets/trend-up-2.png';
import iconContainer from '../assets/Icon Container.png';
import iconContainer1 from '../assets/Icon Container (1).png';
import tickCircle2Icon from '../assets/tick-circle.png';
// import arrowRightSmallIcon from '../assets/arrow-right.png';
import element08 from '../assets/Element 08.png';
import vectorAsset from '../assets/Vector.png';

interface TradeChallengeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

// Chart data for Live Balance Trend
const chartData = [
  { name: 'Jan', value: 500 },
  { name: 'Feb', value: 750 },
  { name: 'Mar', value: 1000 },
  { name: 'Apr', value: 1500 },
  { name: 'May', value: 2000 },
  { name: 'Jun', value: 2750 },
];

const TradeChallenge: React.FC<TradeChallengeProps> = () => {
  const navigate = useNavigate();

  const handleStartTradeChallenge = () => {
    navigate('/trade-challenge-signup');
  };

  return (
    <DashboardLayout className="trade-challenge-container">
      <div className="trade-challenge-content">
        {/* Hero Section */}
        <div className="hero-section">
          <div className="title-section">
            <h1 className="hero-title">
              <span className="amount">$1,000</span>
              <img src={arrowRightIcon} alt="Arrow" className="arrow-icon desktop-arrow" />
              <img src={arrowDownIcon} alt="Arrow Down" className="arrow-icon mobile-arrow" />
              <span className="target">1 Billion in 3,000 Trades</span>
            </h1>
            <img src={groupImage} alt="Decorative line" className="group-line" />
          </div>
          <p className="hero-subtitle">
            Tap into automated trading powered by our founder-built trading bot. Connect your
            exchange, pay a one-time entry fee, and let the bot trade on your behalf. Profit-sharing is
            automatic. We only win when you do.
          </p>
          <button className="get-bot-btn" onClick={handleStartTradeChallenge}>Get the BOT - $100/Year</button>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <div className="content-header">
            <h2 className="content-title">No hype. Just discipline, Bitcoin and a BOT.</h2>
            <p className="content-subtitle">
              Watch Chief Gains Chikueze take on the ultimate 1,000-trade compounding challenge.
              Tracked live and funded by ESVC Capital.
            </p>
          </div>

          {/* Trading Dashboard */}
          <div className="trading-dashboard">
            {/* Live Tracker */}
            <div className="live-tracker">
              <h3 className="tracker-title">Live Tracker</h3>
              <div className="tracker-stats">
                <div className="stat-item">
                  <div className="stat-icon">
                    <img src={dollarSquareIcon} alt="Dollar Square" />
                  </div>
                  <div className="stat-info">
                    <span className="stat-label">TRADE STARTUP CAPITAL</span>
                    <span className="stat-value">$1,000</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">
                    <img src={walletIcon} alt="Wallet" />
                  </div>
                  <div className="stat-info">
                    <span className="stat-label">CURRENT BALANCE</span>
                    <span className="stat-value">$2,166,500</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">
                    <img src={bitcoinConvertIcon} alt="Bitcoin Convert" />
                  </div>
                  <div className="stat-info">
                    <span className="stat-label">TRADE COUNT</span>
                    <span className="stat-value">114 / 1000</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">
                    <img src={trendUp2Icon} alt="Trend Up" />
                  </div>
                  <div className="stat-info">
                    <span className="stat-label">ROI SO FAR</span>
                    <span className="stat-value">+4.3%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Live Balance Trend Chart */}
            <div className="balance-trend">
              <h3 className="chart-title">Live Balance Trend</h3>
              <div className="chart-container">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                      tickFormatter={(value) => {
                        if (value >= 1000) {
                          return `$${(value / 1000).toFixed(2)}M`;
                        }
                        return `$${value}K`;
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#00ff88"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 4, fill: '#00ff88' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Trading Options */}
          <div className="trading-options">
            <button className="option-btn active">
              <span className="option-icon">
                <img src={iconContainer} alt="Auto-trading" />
              </span>
              Auto-trading logic
            </button>
            <button className="option-btn">
              <span className="option-icon">
                <img src={iconContainer1} alt="Custom rules" />
              </span>
              Custom rules by Gaius
            </button>
            <button className="option-btn">
              <span className="option-icon">
                <img src={tickCircle2Icon} alt="Long-term" />
              </span>
              Built for long-term compounding
            </button>
          </div>

          {/* Bottom CTA */}
          <div className="bottom-cta">
            <button className="get-bot-btn-bottom" onClick={handleStartTradeChallenge}>Get the BOT - $100/Year</button>
          </div>
        </div>

        {/* How it Works Section */}
        <div className="how-it-works">
          <div className="how-it-works-left">
            <h2 className="how-it-works-title">
              {/* <img src={element08} alt="Element 08" className="title-decoration" /> */}
              How it Works
              <img src={element08} alt="Element 08" className="title-decoration-end" />
            </h2>
            <p className="how-it-works-subtitle">
              Powered by Tradehouse BOT. Executing every trade 24/7
            </p>
            <div className="steps-list">
              <div className="step-item">
                <span className="step-number">01</span>
                <span className="step-text">Buy Bitcoin/Solana</span>
              </div>
              <div className="step-item">
                <span className="step-number">02</span>
                <span className="step-text">Sell when it's up 1.5%</span>
              </div>
              <div className="step-item">
                <span className="step-number">03</span>
                <span className="step-text">Wait for a 20% dip to re-enter</span>
              </div>
              <div className="step-item">
                <span className="step-number">04</span>
                <span className="step-text">Repeat 1,000 times</span>
              </div>
              <div className="step-item">
                <span className="step-number">05</span>
                <span className="step-text">Never withdraw. Never Stop</span>
              </div>
            </div>
          </div>

          <div className="how-it-works-right">
            <div className="backed-section">
              <h3 className="backed-title">Backed by ESVC Capital</h3>
              <p className="backed-subtitle">A Real Experiment, Backed by Real Fund</p>
              <ul className="backed-list">
                <li>ESVC Capital is not just an investment. It's the engine.</li>
                <li>All Tradehouse BOT subscription revenue flows into the ESVC on-chain treasury.</li>
                <li>Every trade is a proof of utility, not hype.</li>
              </ul>
              <a href="#" className="treasury-link">
                View Public Treasury Wallet →
              </a>
            </div>

            <div className="join-movement">
              <p className="join-movement-text">Join the Movement</p>
              <button className="get-bot-btn-section" onClick={handleStartTradeChallenge}>Get the BOT - $100/Year</button>
            </div>
          </div>
        </div>

        {/* More Than Trading Section */}
        <div className="more-than-trading">
          <div className="more-than-trading-content">
            <h2 className="more-than-trading-title">More Than Trading. Fueling Innovation</h2>
            <p className="more-than-trading-text">
              Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the
              chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to
              support bold solutions from our staking community.
            </p>
            <div className="more-than-trading-buttons">
              <button className="start-staking-btn">
                {/* <img src={vectorAsset} alt="Vector" className="button-decoration" /> */}
                Start Staking Now 
                                <img src={vectorAsset} alt="Vector" className="button-decoration" />

              </button>
              <button className="get-funded-btn">Get Funded</button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradeChallenge;
