import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/VerifyEmail.css';
import emailVerificationIcon from '../assets/email-verification.png';

interface VerifyEmailProps {
  email?: string;
}

type VerificationState = 'default' | 'focused' | 'error' | 'resent' | 'success';

const VerifyEmail: React.FC<VerifyEmailProps> = ({ email = '<EMAIL>' }) => {
  const navigate = useNavigate();
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [currentState, setCurrentState] = useState<VerificationState>('default');
  const [resendTimer, setResendTimer] = useState(0);
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Timer countdown effect for resend
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit
    
    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Check if all fields are filled
    if (newCode.every(digit => digit !== '')) {
      setCurrentState('focused');
    } else if (currentState === 'error') {
      setCurrentState('default');
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
    if (currentState === 'default') {
      setCurrentState('focused');
    }
  };

  const handleBlur = () => {
    setFocusedIndex(null);
    if (verificationCode.every(digit => digit === '')) {
      setCurrentState('default');
    }
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setCurrentState('error');
      return;
    }

    // Simulate verification (replace with actual API call)
    if (code === '123456') {
      setCurrentState('success');
    } else {
      setCurrentState('error');
    }
  };

  const handleResendCode = () => {
    setCurrentState('resent');
    setResendTimer(60); // 1 minute timer
    setVerificationCode(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
  };

  const handleChangeEmailAddress = () => {
    // Navigate back to signup with auto scroll to top
    navigate('/signup');
  };

  const handleContinueToDashboard = () => {
    // Navigate to dashboard or main app
    navigate('/overview');
  };

  const getStateMessage = () => {
    switch (currentState) {
      case 'error':
        return {
          title: 'Verify Your Email to Continue',
          subtitle: `A 6-digit verification code has been sent to ${email}`,
          message: 'Please enter the code below to verify your account',
          errorText: 'Incorrect verification code entered. Please try again.'
        };
      case 'resent':
        return {
          title: 'New Email Sent',
          subtitle: `Please check your inbox [${email}] and enter the new code below to verify your account`,
          message: ''
        };
      case 'success':
        return {
          title: 'Email verified successfully!',
          subtitle: 'You can now proceed to your dashboard and start trading',
          message: ''
        };
      default:
        return {
          title: 'Verify Your Email to Continue',
          subtitle: `A 6-digit verification code has been sent to ${email}`,
          message: 'Please enter the code below to verify your account'
        };
    }
  };

  const stateMessage = getStateMessage();

  if (currentState === 'success') {
    return (
      <div className="verify-email-container">
        <div className="verify-email-form success-form">
          <div className="success-content">
            <div className="success-icon">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                <circle cx="32" cy="32" r="32" fill="#22C55E"/>
                <path d="M20 32L28 40L44 24" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h2 className="success-title">{stateMessage.title}</h2>
            <p className="success-subtitle">{stateMessage.subtitle}</p>
            <button className="continue-button" onClick={handleContinueToDashboard}>
              Continue to Dashboard →
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="verify-email-container">
      <div className={`verify-email-form ${currentState === 'resent' ? 'resent-form' : ''}`}>
        <div className="form-content">
          <div className="email-icon">
            <img src={emailVerificationIcon} alt="Email Verification" width="64" height="64" />
          </div>
          
          <div className="form-header">
            <h2 className="form-title">{stateMessage.title}</h2>
            <p className="form-subtitle">{stateMessage.subtitle}</p>
            {stateMessage.message && <p className="form-message">{stateMessage.message}</p>}
          </div>

          <div className="verification-inputs">
            {verificationCode.map((digit, index) => (
              <input
                key={index}
                ref={el => { inputRefs.current[index] = el; }}
                type="text"
                maxLength={1}
                value={digit}
                onChange={(e) => handleInputChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onFocus={() => handleFocus(index)}
                onBlur={handleBlur}
                className={`verification-input ${
                  currentState === 'error' ? 'error' : 
                  currentState === 'focused' && focusedIndex === index ? 'focused' : ''
                }`}
              />
            ))}
          </div>

          {currentState === 'error' && (
            <p className="error-message">{stateMessage.errorText}</p>
          )}

          <button 
            className="verify-button" 
            onClick={handleVerifyCode}
            disabled={verificationCode.some(digit => digit === '')}
          >
            Verify Code
          </button>

          <div className="form-actions">
            <button 
              className="resend-button" 
              onClick={handleResendCode}
              disabled={resendTimer > 0}
            >
              {resendTimer > 0 ? `Resend Code (${resendTimer}s)` : 'Resend Code'}
            </button>
            
            <button className="change-email-button" onClick={handleChangeEmailAddress}>
              <span>←</span> Change Email Address
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
