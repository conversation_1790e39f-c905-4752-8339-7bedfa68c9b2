import React, { useState } from 'react';
import '../styles/components/StakeESVC.css';
import DashboardLayout from './DashboardLayout';
import StakeESVCModal from './modals/StakeESVCModal';

// Import icons
import cardCoinIcon from '../assets/card-coin.png';
import esvcToken from '../assets/esvc-token.png';
import usdcIcon from '../assets/usdc.png';

const StakeESVC: React.FC = () => {
  const [showStakeModal, setShowStakeModal] = useState(false);

  const handleStakeNow = () => {
    setShowStakeModal(true);
  };

  const handleStakeModalClose = () => {
    setShowStakeModal(false);
  };

  return (
    <DashboardLayout className="stake-esvc-container">
      <div className="stake-esvc-content">
        {/* Hero Section */}
        <div className="hero-section">
          <div className="title-section">
            <h1 className="hero-title">Stake ESVC & Earn Rewards</h1>
            <p className="hero-subtitle">
              Stake your ESVC tokens to earn USDC rewards and unlock exclusive benefits including startup pitch access.
            </p>
          </div>
          <button className="stake-esvc-btn primary" onClick={handleStakeNow}>
            <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
            Start Staking
          </button>
        </div>

        {/* Main Content */}
        <div className="main-content">
          {/* Staking Options */}
          <div className="staking-options">
            <h2 className="section-title">Choose Your Staking Duration</h2>
            <p className="section-subtitle">
              Longer staking periods offer higher APY rewards and additional benefits
            </p>

            <div className="staking-cards">
              <div className="staking-card">
                <div className="card-header">
                  <h4 className="card-title">6 Months</h4>
                  <div className="apy-badge">8.5% APY</div>
                </div>
                <div className="card-content">
                  <div className="benefit-list">
                    <div className="benefit-item">✓ Quarterly rewards distribution</div>
                    <div className="benefit-item">✓ Early withdrawal available</div>
                    <div className="benefit-item">✓ Pitch eligibility after $500</div>
                  </div>
                  <button className="stake-option-btn" onClick={handleStakeNow}>
                    Stake Now
                  </button>
                </div>
              </div>

              <div className="staking-card featured">
                <div className="featured-badge">Most Popular</div>
                <div className="card-header">
                  <h4 className="card-title">12 Months</h4>
                  <div className="apy-badge">12% APY</div>
                </div>
                <div className="card-content">
                  <div className="benefit-list">
                    <div className="benefit-item">✓ Monthly rewards distribution</div>
                    <div className="benefit-item">✓ Higher APY rewards</div>
                    <div className="benefit-item">✓ Priority pitch review</div>
                    <div className="benefit-item">✓ Exclusive events access</div>
                  </div>
                  <button className="stake-option-btn primary" onClick={handleStakeNow}>
                    Stake Now
                  </button>
                </div>
              </div>

              <div className="staking-card">
                <div className="card-header">
                  <h4 className="card-title">24 Months</h4>
                  <div className="apy-badge">15% APY</div>
                </div>
                <div className="card-content">
                  <div className="benefit-list">
                    <div className="benefit-item">✓ Weekly rewards distribution</div>
                    <div className="benefit-item">✓ Maximum APY rewards</div>
                    <div className="benefit-item">✓ VIP pitch sessions</div>
                    <div className="benefit-item">✓ Direct founder access</div>
                  </div>
                  <button className="stake-option-btn" onClick={handleStakeNow}>
                    Stake Now
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* How It Works */}
          <div className="how-it-works">
            <h2 className="section-title">How Staking Works</h2>
            <div className="steps-grid">
              <div className="step-item">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4 className="step-title">Connect Wallet</h4>
                  <p className="step-description">Connect your wallet and ensure you have ESVC tokens</p>
                </div>
              </div>
              <div className="step-item">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4 className="step-title">Choose Duration</h4>
                  <p className="step-description">Select your preferred staking period and APY rate</p>
                </div>
              </div>
              <div className="step-item">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4 className="step-title">Earn Rewards</h4>
                  <p className="step-description">Receive USDC rewards based on your staking duration and amount</p>
                </div>
              </div>
              <div className="step-item">
                <div className="step-number">4</div>
                <div className="step-content">
                  <h4 className="step-title">Get Pitch Access</h4>
                  <p className="step-description">Stake $500+ to unlock startup pitch submission privileges</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="bottom-cta">
            <h3 className="cta-title">Ready to Start Earning?</h3>
            <p className="cta-subtitle">Join thousands of ESVC stakers earning passive income</p>
            <button className="stake-esvc-btn-bottom" onClick={handleStakeNow}>
              <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
              Start Staking Now
            </button>
          </div>
        </div>
      </div>

      {/* Stake ESVC Modal */}
      <StakeESVCModal
        isOpen={showStakeModal}
        onClose={handleStakeModalClose}
      />
    </DashboardLayout>
  );
};

export default StakeESVC;
