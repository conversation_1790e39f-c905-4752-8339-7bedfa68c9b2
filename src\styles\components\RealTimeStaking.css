/* Real-Time Staking Content */
.real-time-staking-content {
  position: relative;
  z-index: 2;
}

/* Page Header - Reuse from Overview */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout - Reuse from Overview */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar styles removed - using SideNav component */

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.staking-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Controls */
.staking-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -42px;
  margin-bottom: 32px;
  gap: 20px;
}

.time-filter {
  position: relative;
}

.filter-select {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 40px 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  min-width: 140px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(191, 65, 41, 0.5);
}

.total-value-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  text-align: center;
}

.total-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.total-amount {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
}

/* Staking List */
.staking-list {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.staking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.staking-item:last-child {
  border-bottom: none;
}

.staking-number {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  min-width: 32px;
}

.staking-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  overflow: visible;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.staking-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 8px;
}

.staking-icon::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: #0e3820;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #262626;
  z-index: 10;
}

.staking-icon::before {
  content: '';
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2344ff44'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 17l-9.2-9.2M7 17V7h10'/%3e%3c/svg%3e");
  background-size: 8px 8px;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 11;
  display: flex;
  align-items: center;
  justify-content: center;
}

.staking-details {
  flex: 1;
}

.staking-type {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.staking-recipient {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #CCCCCC;
}

.staking-amount-time {
  text-align: right;
}

.staking-amount {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #4AFF4A;
  margin-bottom: 4px;
}

.staking-time {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #CCCCCC;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  gap: 16px;
}

.pagination-btn {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.pagination-btn.active {
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  color: #FFFFFF;
  border-color: rgba(191, 65, 41, 0.3);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-center {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-ellipsis {
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  padding: 0 8px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
    gap: 12px;
    justify-content: center;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 180px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }




  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
  }

  .staking-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-select {
    width: 100%;
    padding: 14px 40px 14px 16px;
  }

  .total-value-card {
    padding: 16px;
  }

  .staking-list {
    padding: 16px;
  }

  .staking-item {
    gap: 12px;
    padding: 12px 0;
  }

  .staking-number {
    font-size: 14px;
    min-width: 24px;
  }

  .staking-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    padding: 6px;
  }

  .staking-icon img {
    width: 28px;
    height: 28px;
    border-radius: 6px;
  }

  .staking-icon::after {
    width: 16px;
    height: 16px;
    border-radius: 6px;
    bottom: -4px;
    right: -4px;
    border: 1px solid #262626;
    z-index: 10;
  }

  .staking-icon::before {
    width: 10px;
    height: 10px;
    bottom: -1px;
    right: -1px;
    background-size: 6px 6px;
    z-index: 11;
  }

  .staking-type {
    font-size: 14px;
  }

  .staking-recipient {
    font-size: 12px;
  }

  .staking-amount {
    font-size: 14px;
  }

  .staking-time {
    font-size: 12px;
  }

  .pagination {
    gap: 16px;
    flex-direction: row;
    justify-content: space-between;
  }

  .pagination-center {
    order: 0;
    justify-content: center;
    flex: 1;
    display: flex;
    align-items: center;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    color: #CCCCCC;
  }

  .pagination-center .pagination-btn,
  .pagination-center .pagination-ellipsis {
    display: none;
  }

  .pagination-center::before {
    content: 'Page 1/10';
  }

  .pagination-btn {
    padding: 10px 16px;
    font-size: 13px;
    min-width: auto;
    height: 40px;
    flex-shrink: 0;
  }
}
